"""Model provider abstraction layer for supporting multiple LLM providers."""

from __future__ import annotations

import os
from abc import ABC, abstractmethod
from typing import Any, Optional

from langchain_core.language_models import BaseChatModel
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI


class ModelProvider(ABC):
    """Abstract base class for model providers."""

    @abstractmethod
    def get_chat_model(
        self,
        model_name: str,
        temperature: float = 1.0,
        max_retries: int = 2,
        **kwargs: Any,
    ) -> BaseChatModel:
        """Get a chat model instance."""
        pass

    @abstractmethod
    def supports_web_search(self) -> bool:
        """Check if the provider supports web search with grounding metadata."""
        pass

    @abstractmethod
    def get_web_search_client(self) -> Optional[Any]:
        """Get the web search client if supported."""
        pass

    @abstractmethod
    def validate_configuration(self) -> bool:
        """Validate that the provider is properly configured."""
        pass


class GeminiProvider(ModelProvider):
    """Google Gemini model provider."""

    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")

    def get_chat_model(
        self,
        model_name: str,
        temperature: float = 1.0,
        max_retries: int = 2,
        **kwargs: Any,
    ) -> BaseChatModel:
        """Get a Gemini chat model instance."""
        return ChatGoogleGenerativeAI(
            model=model_name,
            temperature=temperature,
            max_retries=max_retries,
            api_key=self.api_key,
            **kwargs,
        )

    def supports_web_search(self) -> bool:
        """Gemini supports web search with grounding metadata."""
        return True

    def get_web_search_client(self) -> Optional[Any]:
        """Get the Google GenAI client for web search."""
        try:
            from google.genai import Client
            return Client(api_key=self.api_key)
        except ImportError:
            return None

    def validate_configuration(self) -> bool:
        """Validate Gemini configuration."""
        return self.api_key is not None


class OpenRouterProvider(ModelProvider):
    """OpenRouter model provider."""

    def __init__(self):
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1"

    def get_chat_model(
        self,
        model_name: str,
        temperature: float = 1.0,
        max_retries: int = 2,
        **kwargs: Any,
    ) -> BaseChatModel:
        """Get an OpenRouter chat model instance."""
        # Remove any kwargs that are specific to other providers
        filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ['api_key']}
        
        return ChatOpenAI(
            model=model_name,
            temperature=temperature,
            max_retries=max_retries,
            api_key=self.api_key,
            base_url=self.base_url,
            **filtered_kwargs,
        )

    def supports_web_search(self) -> bool:
        """OpenRouter does not support web search with grounding metadata."""
        return False

    def get_web_search_client(self) -> Optional[Any]:
        """OpenRouter does not provide web search client."""
        return None

    def validate_configuration(self) -> bool:
        """Validate OpenRouter configuration."""
        return self.api_key is not None


# Model mappings for different providers
GEMINI_MODELS = {
    "query_generator": "gemini-2.0-flash",
    "reflection": "gemini-2.5-flash-preview-04-17",
    "answer": "gemini-2.5-pro-preview-05-06",
}

OPENROUTER_MODELS = {
    "query_generator": "anthropic/claude-3.5-sonnet",
    "reflection": "anthropic/claude-3.5-sonnet",
    "answer": "anthropic/claude-3.5-sonnet",
}

# Alternative OpenRouter model configurations
OPENROUTER_MODELS_ALTERNATIVE = {
    "query_generator": "openai/gpt-4o",
    "reflection": "openai/gpt-4o",
    "answer": "openai/gpt-4o",
}


def get_provider(provider_name: Optional[str] = None) -> ModelProvider:
    """Get a model provider instance."""
    if provider_name is None:
        provider_name = os.getenv("MODEL_PROVIDER", "gemini").lower()

    if provider_name == "gemini":
        provider = GeminiProvider()
    elif provider_name == "openrouter":
        provider = OpenRouterProvider()
    else:
        raise ValueError(f"Unknown provider: {provider_name}")

    if not provider.validate_configuration():
        raise ValueError(f"Provider {provider_name} is not properly configured")

    return provider


def get_model_name(provider: ModelProvider, model_type: str, custom_model: Optional[str] = None) -> str:
    """Get the appropriate model name for a provider and model type."""
    # If a custom model is provided, use it directly
    if custom_model:
        return custom_model

    # Use provider-specific defaults
    if isinstance(provider, GeminiProvider):
        return GEMINI_MODELS.get(model_type, GEMINI_MODELS["query_generator"])
    elif isinstance(provider, OpenRouterProvider):
        return OPENROUTER_MODELS.get(model_type, OPENROUTER_MODELS["query_generator"])
    else:
        raise ValueError(f"Unknown provider type: {type(provider)}")
