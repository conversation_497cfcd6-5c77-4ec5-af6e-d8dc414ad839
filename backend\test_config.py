#!/usr/bin/env python3
"""
Configuration test script that doesn't require API keys.

This script tests the configuration and provider setup without making actual API calls.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all modules can be imported correctly."""
    print("🧪 Testing Module Imports...")
    
    try:
        from agent.providers import get_provider, GeminiProvider, OpenRouterProvider, get_model_name
        print("✅ Provider modules imported successfully")
    except ImportError as e:
        print(f"❌ Provider import error: {e}")
        return False
    
    try:
        from agent.configuration import Configuration
        print("✅ Configuration module imported successfully")
    except ImportError as e:
        print(f"❌ Configuration import error: {e}")
        return False
    
    try:
        from agent.tools_and_schemas import SearchQueryList, Reflection
        print("✅ Tools and schemas imported successfully")
    except ImportError as e:
        print(f"❌ Tools and schemas import error: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration class functionality."""
    print("\n🧪 Testing Configuration...")
    
    try:
        from agent.configuration import Configuration
        
        # Test default configuration
        config = Configuration()
        print(f"✅ Default configuration created")
        print(f"   - Model provider: {config.model_provider}")
        print(f"   - Query generator model: {config.query_generator_model}")
        print(f"   - Reflection model: {config.reflection_model}")
        print(f"   - Answer model: {config.answer_model}")
        
        # Test configuration with custom values
        custom_config = Configuration(
            model_provider="openrouter",
            query_generator_model="anthropic/claude-3.5-sonnet",
            reflection_model="openai/gpt-4o",
            answer_model="anthropic/claude-3-opus"
        )
        print(f"✅ Custom configuration created")
        print(f"   - Model provider: {custom_config.model_provider}")
        print(f"   - Query generator model: {custom_config.query_generator_model}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test error: {e}")
        return False

def test_provider_classes():
    """Test provider class instantiation without API keys."""
    print("\n🧪 Testing Provider Classes...")
    
    try:
        from agent.providers import GeminiProvider, OpenRouterProvider
        
        # Test Gemini provider
        gemini_provider = GeminiProvider()
        print(f"✅ Gemini provider created")
        print(f"   - Supports web search: {gemini_provider.supports_web_search()}")
        print(f"   - Configured: {gemini_provider.validate_configuration()}")
        
        # Test OpenRouter provider
        openrouter_provider = OpenRouterProvider()
        print(f"✅ OpenRouter provider created")
        print(f"   - Supports web search: {openrouter_provider.supports_web_search()}")
        print(f"   - Configured: {openrouter_provider.validate_configuration()}")
        print(f"   - Base URL: {openrouter_provider.base_url}")
        
        return True
    except Exception as e:
        print(f"❌ Provider class test error: {e}")
        return False

def test_model_mappings():
    """Test model name mapping functionality."""
    print("\n🧪 Testing Model Mappings...")
    
    try:
        from agent.providers import get_model_name, GeminiProvider, OpenRouterProvider
        from agent.providers import GEMINI_MODELS, OPENROUTER_MODELS
        
        # Test Gemini model mappings
        gemini_provider = GeminiProvider()
        query_model = get_model_name(gemini_provider, "query_generator")
        reflection_model = get_model_name(gemini_provider, "reflection")
        answer_model = get_model_name(gemini_provider, "answer")
        
        print(f"✅ Gemini model mappings:")
        print(f"   - Query generator: {query_model}")
        print(f"   - Reflection: {reflection_model}")
        print(f"   - Answer: {answer_model}")
        
        # Test OpenRouter model mappings
        openrouter_provider = OpenRouterProvider()
        query_model = get_model_name(openrouter_provider, "query_generator")
        reflection_model = get_model_name(openrouter_provider, "reflection")
        answer_model = get_model_name(openrouter_provider, "answer")
        
        print(f"✅ OpenRouter model mappings:")
        print(f"   - Query generator: {query_model}")
        print(f"   - Reflection: {reflection_model}")
        print(f"   - Answer: {answer_model}")
        
        # Test custom model override
        custom_model = get_model_name(openrouter_provider, "query_generator", "custom/model-name")
        print(f"✅ Custom model override: {custom_model}")
        
        return True
    except Exception as e:
        print(f"❌ Model mapping test error: {e}")
        return False

def test_graph_imports():
    """Test that the updated graph module can be imported."""
    print("\n🧪 Testing Graph Module...")
    
    try:
        from agent.graph import graph
        print("✅ Graph module imported successfully")
        print(f"   - Graph type: {type(graph)}")
        return True
    except ImportError as e:
        print(f"❌ Graph import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Graph test error: {e}")
        return False

def test_environment_handling():
    """Test environment variable handling."""
    print("\n🧪 Testing Environment Variable Handling...")
    
    try:
        from agent.providers import get_provider
        
        # Test with no provider specified (should default to gemini)
        os.environ.pop('MODEL_PROVIDER', None)
        try:
            provider = get_provider()
            print("❌ Should have failed without API key")
        except ValueError as e:
            print("✅ Correctly failed when no API key is configured")
        
        # Test with explicit provider selection
        try:
            provider = get_provider("openrouter")
            print("❌ Should have failed without API key")
        except ValueError as e:
            print("✅ Correctly failed when OpenRouter API key is not configured")
        
        try:
            provider = get_provider("gemini")
            print("❌ Should have failed without API key")
        except ValueError as e:
            print("✅ Correctly failed when Gemini API key is not configured")
        
        # Test invalid provider
        try:
            provider = get_provider("invalid_provider")
            print("❌ Should have failed with invalid provider")
        except ValueError as e:
            print("✅ Correctly failed with invalid provider name")
        
        return True
    except Exception as e:
        print(f"❌ Environment handling test error: {e}")
        return False

def main():
    """Run all configuration tests."""
    print("🚀 Starting Configuration Tests")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Run tests
    tests = [
        test_imports,
        test_configuration,
        test_provider_classes,
        test_model_mappings,
        test_graph_imports,
        test_environment_handling,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"🏁 Configuration Tests Completed: {passed}/{total} passed")
    
    if passed == total:
        print("✅ All configuration tests passed! The OpenRouter integration is ready.")
    else:
        print("❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
