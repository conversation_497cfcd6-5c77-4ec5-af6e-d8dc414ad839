#!/usr/bin/env python3
"""
Test script for verifying different model configurations with OpenRouter.

This script tests various model combinations to ensure they work correctly.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.providers import get_provider, get_model_name
from agent.configuration import Configuration
from agent.tools_and_schemas import SearchQueryList

def test_model_configuration():
    """Test the current model configuration."""
    print("🧪 Testing Current Model Configuration")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check provider configuration
    provider_name = os.getenv("MODEL_PROVIDER", "gemini")
    print(f"Model Provider: {provider_name}")
    
    if provider_name == "openrouter":
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("❌ OPENROUTER_API_KEY not found!")
            return False
        print(f"✅ OpenRouter API Key: {api_key[:10]}...")
    elif provider_name == "gemini":
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            print("❌ GEMINI_API_KEY not found!")
            return False
        print(f"✅ Gemini API Key: {api_key[:10]}...")
    
    # Test provider initialization
    try:
        provider = get_provider(provider_name)
        print(f"✅ Provider initialized: {type(provider).__name__}")
    except Exception as e:
        print(f"❌ Provider initialization failed: {e}")
        return False
    
    # Test configuration
    config = Configuration.from_runnable_config()
    print(f"\n📋 Configuration:")
    print(f"   Query Generator Model: {config.query_generator_model}")
    print(f"   Reflection Model: {config.reflection_model}")
    print(f"   Answer Model: {config.answer_model}")
    print(f"   Model Provider: {config.model_provider}")
    
    # Test model name resolution
    print(f"\n🔍 Resolved Model Names:")
    try:
        query_model = get_model_name(provider, "query_generator", config.query_generator_model)
        reflection_model = get_model_name(provider, "reflection", config.reflection_model)
        answer_model = get_model_name(provider, "answer", config.answer_model)
        
        print(f"   Query Generator: {query_model}")
        print(f"   Reflection: {reflection_model}")
        print(f"   Answer: {answer_model}")
    except Exception as e:
        print(f"❌ Model name resolution failed: {e}")
        return False
    
    return True

def test_model_creation():
    """Test creating models with current configuration."""
    print(f"\n🧪 Testing Model Creation")
    print("=" * 30)
    
    try:
        provider = get_provider()
        config = Configuration.from_runnable_config()
        
        # Test query generator model
        print("Creating Query Generator Model...")
        query_model_name = get_model_name(provider, "query_generator", config.query_generator_model)
        query_model = provider.get_chat_model(query_model_name, temperature=0.7)
        print(f"✅ Query Generator: {query_model_name}")
        
        # Test reflection model
        print("Creating Reflection Model...")
        reflection_model_name = get_model_name(provider, "reflection", config.reflection_model)
        reflection_model = provider.get_chat_model(reflection_model_name, temperature=0.5)
        print(f"✅ Reflection: {reflection_model_name}")
        
        # Test answer model
        print("Creating Answer Model...")
        answer_model_name = get_model_name(provider, "answer", config.answer_model)
        answer_model = provider.get_chat_model(answer_model_name, temperature=0.3)
        print(f"✅ Answer: {answer_model_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        return False

def test_simple_completions():
    """Test simple completions with each model type."""
    print(f"\n🧪 Testing Simple Completions")
    print("=" * 35)
    
    try:
        provider = get_provider()
        config = Configuration.from_runnable_config()
        
        # Test query generator
        print("Testing Query Generator...")
        query_model_name = get_model_name(provider, "query_generator", config.query_generator_model)
        query_model = provider.get_chat_model(query_model_name, temperature=0)
        response = query_model.invoke("Generate a search query about renewable energy. Respond with just the query.")
        print(f"✅ Query Generator Response: {response.content[:100]}...")
        
        # Test reflection model
        print("Testing Reflection Model...")
        reflection_model_name = get_model_name(provider, "reflection", config.reflection_model)
        reflection_model = provider.get_chat_model(reflection_model_name, temperature=0)
        response = reflection_model.invoke("Is the information 'Solar panels convert sunlight to electricity' sufficient to answer 'How does renewable energy work?' Answer yes or no.")
        print(f"✅ Reflection Response: {response.content[:100]}...")
        
        # Test answer model
        print("Testing Answer Model...")
        answer_model_name = get_model_name(provider, "answer", config.answer_model)
        answer_model = provider.get_chat_model(answer_model_name, temperature=0)
        response = answer_model.invoke("Explain renewable energy in one sentence.")
        print(f"✅ Answer Response: {response.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Completion test failed: {e}")
        return False

def test_structured_output():
    """Test structured output with query generator model."""
    print(f"\n🧪 Testing Structured Output")
    print("=" * 35)
    
    try:
        provider = get_provider()
        config = Configuration.from_runnable_config()
        
        # Test structured output with query generator
        query_model_name = get_model_name(provider, "query_generator", config.query_generator_model)
        query_model = provider.get_chat_model(query_model_name, temperature=0.7)
        structured_model = query_model.with_structured_output(SearchQueryList)
        
        prompt = "Generate 2 search queries about artificial intelligence. Each should have a rationale."
        response = structured_model.invoke(prompt)
        
        print(f"✅ Generated {len(response.query)} structured queries:")
        for i, query in enumerate(response.query, 1):
            print(f"   {i}. {query['query']}")
            print(f"      Rationale: {query['rationale'][:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Structured output test failed: {e}")
        return False

def main():
    """Run all model tests."""
    print("🚀 Model Configuration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_model_configuration),
        ("Model Creation", test_model_creation),
        ("Simple Completions", test_simple_completions),
        ("Structured Output", test_structured_output),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Your model configuration is working correctly.")
    else:
        print("❌ Some tests failed. Please check your configuration and API keys.")

if __name__ == "__main__":
    main()
