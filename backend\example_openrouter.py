#!/usr/bin/env python3
"""
Example script demonstrating how to use the LangGraph agent with OpenRouter.

This script shows how to:
1. Configure the agent to use OpenRouter models
2. Run a research query
3. Handle the response

Prerequisites:
- Set OPENROUTER_API_KEY in your .env file
- Set MODEL_PROVIDER=openrouter in your .env file
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.graph import graph
from langchain_core.messages import HumanMessage

async def run_research_example():
    """Run a research example using OpenRouter models."""
    
    # Load environment variables
    load_dotenv()
    
    # Check configuration
    if not os.getenv("OPENROUTER_API_KEY"):
        print("❌ OPENROUTER_API_KEY not found in environment variables.")
        print("Please set your OpenRouter API key in the .env file.")
        return
    
    if os.getenv("MODEL_PROVIDER", "gemini").lower() != "openrouter":
        print("⚠️  MODEL_PROVIDER is not set to 'openrouter'.")
        print("The agent will use the default provider. To use OpenRouter, set MODEL_PROVIDER=openrouter in your .env file.")
    
    print("🚀 Starting OpenRouter Research Example")
    print("=" * 50)
    
    # Define the research query
    research_query = "What are the latest developments in quantum computing and their potential applications?"
    
    print(f"Research Query: {research_query}")
    print("\n🔍 Starting research process...")
    
    # Configure the agent
    config = {
        "configurable": {
            "model_provider": "openrouter",
            "query_generator_model": "anthropic/claude-3.5-sonnet",
            "reflection_model": "anthropic/claude-3.5-sonnet", 
            "answer_model": "anthropic/claude-3.5-sonnet",
            "number_of_initial_queries": 3,
            "max_research_loops": 2,
        }
    }
    
    # Create the initial state
    initial_state = {
        "messages": [HumanMessage(content=research_query)],
        "search_query": [],
        "web_research_result": [],
        "sources_gathered": [],
        "initial_search_query_count": 3,
        "max_research_loops": 2,
        "research_loop_count": 0,
        "reasoning_model": "anthropic/claude-3.5-sonnet"
    }
    
    try:
        # Run the agent
        print("🤖 Agent is processing your request...")
        
        # Stream the results
        async for event in graph.astream(initial_state, config=config):
            for node_name, node_output in event.items():
                print(f"\n📍 Node: {node_name}")
                
                if node_name == "generate_query":
                    if "query_list" in node_output:
                        print(f"   Generated {len(node_output['query_list'])} search queries:")
                        for i, query in enumerate(node_output['query_list'], 1):
                            print(f"   {i}. {query['query']}")
                
                elif node_name == "web_research":
                    if "web_research_result" in node_output:
                        print(f"   Research completed for query")
                        # Print first 200 characters of the research result
                        result = node_output['web_research_result'][0]
                        preview = result[:200] + "..." if len(result) > 200 else result
                        print(f"   Preview: {preview}")
                
                elif node_name == "reflection":
                    if "is_sufficient" in node_output:
                        sufficient = node_output['is_sufficient']
                        print(f"   Research sufficient: {sufficient}")
                        if not sufficient and "knowledge_gap" in node_output:
                            print(f"   Knowledge gap: {node_output['knowledge_gap']}")
                        if "follow_up_queries" in node_output:
                            print(f"   Follow-up queries: {len(node_output['follow_up_queries'])}")
                
                elif node_name == "finalize_answer":
                    if "messages" in node_output:
                        final_answer = node_output['messages'][0].content
                        print(f"   ✅ Final answer generated ({len(final_answer)} characters)")
        
        # Get the final state
        final_state = await graph.ainvoke(initial_state, config=config)
        
        print("\n" + "=" * 50)
        print("🎯 FINAL RESEARCH RESULT")
        print("=" * 50)
        
        if final_state.get("messages"):
            final_answer = final_state["messages"][-1].content
            print(final_answer)
        
        if final_state.get("sources_gathered"):
            print(f"\n📚 Sources used: {len(final_state['sources_gathered'])}")
            for i, source in enumerate(final_state['sources_gathered'][:5], 1):  # Show first 5 sources
                print(f"   {i}. {source.get('value', 'N/A')}")
        else:
            print("\n📚 No external sources (using model knowledge)")
        
        print(f"\n📊 Research Statistics:")
        print(f"   - Research loops: {final_state.get('research_loop_count', 0)}")
        print(f"   - Total queries: {len(final_state.get('search_query', []))}")
        print(f"   - Model provider: OpenRouter")
        
    except Exception as e:
        print(f"\n❌ Error during research: {e}")
        print("Please check your configuration and API key.")

def main():
    """Main function to run the example."""
    print("OpenRouter Research Agent Example")
    print("This example demonstrates using the LangGraph agent with OpenRouter models.")
    print()
    
    # Run the async example
    asyncio.run(run_research_example())

if __name__ == "__main__":
    main()
