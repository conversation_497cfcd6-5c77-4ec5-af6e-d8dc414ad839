#!/usr/bin/env python3
"""
Test script for verifying OpenRouter and Gemini provider implementations.

This script tests both providers to ensure they work correctly and can be used
interchangeably in the LangGraph agent.
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.providers import get_provider, GeminiProvider, OpenRouterProvider
from agent.tools_and_schemas import SearchQueryList

def test_provider_initialization():
    """Test that providers can be initialized correctly."""
    print("🧪 Testing Provider Initialization...")
    
    # Test OpenRouter provider
    try:
        if os.getenv("OPENROUTER_API_KEY"):
            openrouter_provider = OpenRouterProvider()
            if openrouter_provider.validate_configuration():
                print("✅ OpenRouter provider initialized and configured")
            else:
                print("❌ OpenRouter provider not properly configured")
        else:
            print("⚠️  OPENROUTER_API_KEY not set, skipping OpenRouter test")
    except Exception as e:
        print(f"❌ OpenRouter provider error: {e}")
    
    # Test Gemini provider
    try:
        if os.getenv("GEMINI_API_KEY"):
            gemini_provider = GeminiProvider()
            if gemini_provider.validate_configuration():
                print("✅ Gemini provider initialized and configured")
            else:
                print("❌ Gemini provider not properly configured")
        else:
            print("⚠️  GEMINI_API_KEY not set, skipping Gemini test")
    except Exception as e:
        print(f"❌ Gemini provider error: {e}")

def test_model_creation():
    """Test that models can be created from providers."""
    print("\n🧪 Testing Model Creation...")
    
    # Test OpenRouter model creation
    try:
        if os.getenv("OPENROUTER_API_KEY"):
            provider = get_provider("openrouter")
            model = provider.get_chat_model("anthropic/claude-3.5-sonnet", temperature=0.7)
            print("✅ OpenRouter model created successfully")
        else:
            print("⚠️  Skipping OpenRouter model creation test")
    except Exception as e:
        print(f"❌ OpenRouter model creation error: {e}")
    
    # Test Gemini model creation
    try:
        if os.getenv("GEMINI_API_KEY"):
            provider = get_provider("gemini")
            model = provider.get_chat_model("gemini-2.0-flash", temperature=0.7)
            print("✅ Gemini model created successfully")
        else:
            print("⚠️  Skipping Gemini model creation test")
    except Exception as e:
        print(f"❌ Gemini model creation error: {e}")

def test_simple_completion():
    """Test simple text completion with available providers."""
    print("\n🧪 Testing Simple Completions...")
    
    test_prompt = "What is the capital of France? Please respond with just the city name."
    
    # Test OpenRouter completion
    try:
        if os.getenv("OPENROUTER_API_KEY"):
            provider = get_provider("openrouter")
            model = provider.get_chat_model("anthropic/claude-3.5-sonnet", temperature=0)
            response = model.invoke(test_prompt)
            print(f"✅ OpenRouter response: {response.content.strip()}")
        else:
            print("⚠️  Skipping OpenRouter completion test")
    except Exception as e:
        print(f"❌ OpenRouter completion error: {e}")
    
    # Test Gemini completion
    try:
        if os.getenv("GEMINI_API_KEY"):
            provider = get_provider("gemini")
            model = provider.get_chat_model("gemini-2.0-flash", temperature=0)
            response = model.invoke(test_prompt)
            print(f"✅ Gemini response: {response.content.strip()}")
        else:
            print("⚠️  Skipping Gemini completion test")
    except Exception as e:
        print(f"❌ Gemini completion error: {e}")

def test_structured_output():
    """Test structured output functionality."""
    print("\n🧪 Testing Structured Output...")
    
    test_prompt = """Generate 2 search queries about renewable energy. 
    Each query should have a rationale explaining why it's useful for research."""
    
    # Test OpenRouter structured output
    try:
        if os.getenv("OPENROUTER_API_KEY"):
            provider = get_provider("openrouter")
            model = provider.get_chat_model("anthropic/claude-3.5-sonnet", temperature=0.7)
            structured_model = model.with_structured_output(SearchQueryList)
            response = structured_model.invoke(test_prompt)
            print(f"✅ OpenRouter structured output: {len(response.query)} queries generated")
            for i, query in enumerate(response.query, 1):
                print(f"   {i}. {query['query']}")
        else:
            print("⚠️  Skipping OpenRouter structured output test")
    except Exception as e:
        print(f"❌ OpenRouter structured output error: {e}")
    
    # Test Gemini structured output
    try:
        if os.getenv("GEMINI_API_KEY"):
            provider = get_provider("gemini")
            model = provider.get_chat_model("gemini-2.0-flash", temperature=0.7)
            structured_model = model.with_structured_output(SearchQueryList)
            response = structured_model.invoke(test_prompt)
            print(f"✅ Gemini structured output: {len(response.query)} queries generated")
            for i, query in enumerate(response.query, 1):
                print(f"   {i}. {query['query']}")
        else:
            print("⚠️  Skipping Gemini structured output test")
    except Exception as e:
        print(f"❌ Gemini structured output error: {e}")

def test_web_search_capabilities():
    """Test web search capabilities of providers."""
    print("\n🧪 Testing Web Search Capabilities...")
    
    # Test OpenRouter (should not support web search)
    try:
        if os.getenv("OPENROUTER_API_KEY"):
            provider = get_provider("openrouter")
            supports_search = provider.supports_web_search()
            search_client = provider.get_web_search_client()
            print(f"✅ OpenRouter web search support: {supports_search} (expected: False)")
            print(f"✅ OpenRouter search client: {search_client} (expected: None)")
        else:
            print("⚠️  Skipping OpenRouter web search test")
    except Exception as e:
        print(f"❌ OpenRouter web search test error: {e}")
    
    # Test Gemini (should support web search)
    try:
        if os.getenv("GEMINI_API_KEY"):
            provider = get_provider("gemini")
            supports_search = provider.supports_web_search()
            search_client = provider.get_web_search_client()
            print(f"✅ Gemini web search support: {supports_search} (expected: True)")
            print(f"✅ Gemini search client: {type(search_client).__name__ if search_client else None}")
        else:
            print("⚠️  Skipping Gemini web search test")
    except Exception as e:
        print(f"❌ Gemini web search test error: {e}")

def main():
    """Run all tests."""
    print("🚀 Starting Provider Tests")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check for API keys
    has_openrouter = bool(os.getenv("OPENROUTER_API_KEY"))
    has_gemini = bool(os.getenv("GEMINI_API_KEY"))
    
    print(f"OpenRouter API Key: {'✅ Set' if has_openrouter else '❌ Not set'}")
    print(f"Gemini API Key: {'✅ Set' if has_gemini else '❌ Not set'}")
    
    if not has_openrouter and not has_gemini:
        print("\n❌ No API keys found. Please set OPENROUTER_API_KEY or GEMINI_API_KEY in your .env file.")
        return
    
    # Run tests
    test_provider_initialization()
    test_model_creation()
    test_simple_completion()
    test_structured_output()
    test_web_search_capabilities()
    
    print("\n" + "=" * 50)
    print("🏁 Tests completed!")

if __name__ == "__main__":
    main()
